import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import DesignerContainer from '../../Components/IDDesigner/DesignerContainer';
import { loadTemplate } from '../../redux/idDesignerSlice';
import { getBadgeTemplateById } from '../../api/identity';

const IDDesigner = () => {
  const { templateId } = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    const loadTemplateData = async () => {
      if (templateId) {
        try {
          const response = await getBadgeTemplateById(templateId);
          const templateData = response.data;

          // Load the template into the designer
          dispatch(loadTemplate({
            id: templateData.id,
            name: templateData.name,
            version: templateData.version || '1.0.0',
            lastModified: templateData.updated_at || templateData.created_at,
            canvasConfig: templateData.config?.canvasConfig || {},
            elements: templateData.config?.elements || []
          }));

          toast.success(`Template "${templateData.name}" loaded successfully`);
        } catch (error) {
          console.error('Error loading template:', error);
          toast.error('Failed to load template');
        }
      }
    };

    loadTemplateData();
  }, [templateId, dispatch]);

  return (
    <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
      <h2 className="font-normal text-[24px] mb-4 text-[#4F2683]">
        ID Card Designer{templateId ? ` - Template ${templateId}` : ''}
      </h2>
      <div className="flex-1 overflow-hidden">
        <DesignerContainer />
      </div>
    </div>
  );
};

export default IDDesigner;
