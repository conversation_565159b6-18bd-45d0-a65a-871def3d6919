import api from "./";

/**
 * Retrieves a paginated list of appointments.
 *
 * @param {object} params - Query parameters including page and limit.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated appointment details.
 */

// Only add sortBy and sortOrder if they are provided in params
export const getIdentity = async (params = {}) => {
  const query = {};
  if (params.status !== undefined) query.status = params.status;
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;
  const response = await api.get("identity/hub", { params: query });
  return response.data;
};

export const createIdentity = async (identityData) => {
  const response = await api.post("identity" , identityData);
  return response.data;
};

/**
 * get identity details.
 *
 * @param {string} identity_id - The unique identifier of the facility to update.
 * @param {object} identityData - The data to update. This may include properties such as name, facility_code, facility_type, time_zone, phone, email, status, etc.
 * @returns {Promise<any>} A promise that resolves to the response data containing the updated facility.
 */
export const getIdentityDetails = async (identity_id) => {
  const response = await api.get(`identity/basic/${identity_id}`);
  return response.data;
};
export const getIdentityById = async (identity_id) => {
  const response = await api.get(`identity/${identity_id}`);
  return response.data;
};

// ############################ CARD API's###########################
export const createCard = async (cardData) => {
  const response = await api.post("identity/card" , cardData);
  return response.data;
};

export const viewCard = async () => {
  const response = await api.get("identity/cards");
  return response.data;
};
export const viewCardByIdentity = async (identity_id , params={}) => {
    const query = {};

  if (params.search) query.search = params.search;
  const response = await api.get(`identity/card/access/${identity_id}`,  { params: query });
  return response.data.data || [];
};
export const editCard = async (card_id , cardData) => {
  const response = await api.patch(`identity/card/${card_id}` , cardData);
  return response.data;
};
export const deleteCard = async (card_id) => {
  const response = await api.delete(`identity/card/${card_id}` );
  return response.data;
};

// Only add sortBy and sortOrder if they are provided in params
export const cardList = async (params = {}) => {
  const query = {};
  if (params.status !== undefined) query.status = params.status;
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;
  const response = await api.get("identity/card-list", { params: query });
  return response.data.data;
};

// ############################# IDENTITY ACCESS API's ###########################

export const createIdentityAccess = async (IdentityAccessData) => {
  const response = await api.post("identity/identity-access", IdentityAccessData);
  return response.data;
};
export const getAccessLevel = async (params = {}) => {
  const response = await api.get("identity/access-level-names",  { params });
  return response.data.data || [];
};
export const getCardNumber = async () => {
  const response = await api.get("identity/card-numbers");
  return response.data;
};
export const getIdentityAccessList = async () => {
  const response = await api.get("identity/identity-access");
  return response.data;
};

// Only add sortBy and sortOrder if they are provided in params
export const getIdentityAccessDetails = async (params = {}) => {
  const query = {};
  if (params.status !== undefined) query.status = params.status;
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;
  const response = await api.get("identity/identity-access-details", { params: query });
  return response.data;;
};

export const getIdentityAccessById = async (identity_access_id) => {
  const response = await api.get(`identity/identity-access/${identity_access_id}`);
  return response.data;
};
export const getIdentityAccessByIdentity = async (identity_id , params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  const response = await api.get(`identity/identity-access/access/${identity_id}`,  { params: query });
  return response.data.data || [];
};

export const updateIdentityAccess = async (identity_access_id, identityData) => {
  const response = await api.patch(`identity/identity-access/${identity_access_id}`, identityData);
  return response.data;
};
export const deleteIdentityAccess = async (identity_access_id) => {
  const response = await api.delete(`identity/identity-access/${identity_access_id}`);
  return response.data;
};
export const getIdentityOrganization = async (identity_id) => {
  const response = await api.get(`identity/organization/`,  {
    params: { identity_id },});
  return response.data;
};



// ############################# UPDATE API's ###########################
export const updateOrganization = async (organizationData) => {
  const response = await api.patch(`identity/organization`, organizationData);
  return response.data;
};
export const updateIdentity = async (identity_id , identityData) => {
  const response = await api.patch(`identity/${identity_id}`, identityData);
  return response.data;
};
export const updateFacility = async (facilityData) => {
  const response = await api.patch(`identity/facility`, facilityData);
  return response.data;
};
export const fetchFacility = async () => {
  const response = await api.get("master-data/facilities");
  return response.data;
};

// #################### DELEGATE API'S ###################
export const createDelegate = async (DelegateData) => {
  const response = await api.post("/delegates", DelegateData);
  return response.data;
};

export const getDelegates = async (identity_id , params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  const response = await api.get(`delegates?identity_id=${identity_id}`,  { params: query });
  return response.data.data.data || [];
};

export const updateDelegates = async (delegate_id , DelegateData) => {
  const response = await api.put(`delegates/${delegate_id}`, DelegateData);
  return response.data;
};
export const deleteDelegates = async (delegate_id ) => {
  const response = await api.delete(`delegates/${delegate_id}`);
  return response.data;
};


// ################### Document API's ##################
export const createDocument = async (DocumentData) => {
  try {
    // Check if DocumentData is FormData (for file uploads)
    const isFormData = DocumentData instanceof FormData;

    const config = {
      headers: {
        'Content-Type': isFormData ? 'multipart/form-data' : 'application/json'
      }
    };

    const response = await api.post(`documents`, DocumentData, config);
    return response.data;
  } catch (error) {
    // Handle country_id error specifically
    if (error.response?.data?.message?.includes('country_id') ||
        error.response?.data?.message?.includes('column') ||
        error.response?.data?.message?.includes('does not exist')) {
      console.warn("Backend country_id error detected in createDocument, retrying without country_id...");

      // Remove country_id from FormData or regular object
      let cleanData = DocumentData;
      if (DocumentData instanceof FormData) {
        cleanData = new FormData();
        for (let [key, value] of DocumentData.entries()) {
          if (key !== 'country_id') {
            cleanData.append(key, value);
          }
        }
      } else {
        cleanData = { ...DocumentData };
        delete cleanData.country_id;
      }

      const isFormData = cleanData instanceof FormData;
      const config = {
        headers: {
          'Content-Type': isFormData ? 'multipart/form-data' : 'application/json'
        }
      };

      const response = await api.post(`documents`, cleanData, config);
      return response.data;
    }
    throw error;
  }
};

export const getDocument = async (identity_id, params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;
  if (params.page) query.page = params.page;
  if (params.limit) query.limit = params.limit;

  try {
    const response = await api.get(`documents?identity_id=${identity_id}`, { params: query });
    return response.data.data || response.data || [];
  } catch (error) {
    // Handle country_id error specifically
    if (error.response?.data?.message?.includes('country_id') ||
        error.response?.data?.message?.includes('column') ||
        error.response?.data?.message?.includes('does not exist')) {
      console.warn("Backend country_id error detected in getDocument, returning empty array...");
      // Return empty array as fallback
      return [];
    }
    throw error;
  }
};

export const getDocumentById = async (document_id) => {
  const response = await api.get(`documents/detail/${document_id}`);
  return response.data;
};

export const updateDocument = async (document_id, DocumentData) => {
  try {
    const response = await api.put(`documents/${document_id}`, DocumentData);
    return response.data;
  } catch (error) {
    // Handle country_id error specifically
    if (error.response?.data?.message?.includes('country_id') ||
        error.response?.data?.message?.includes('column') ||
        error.response?.data?.message?.includes('does not exist')) {
      console.warn("Backend country_id error detected in updateDocument, retrying without country_id...");

      // Remove any country_id field if it exists
      const cleanData = { ...DocumentData };
      delete cleanData.country_id;

      const response = await api.put(`documents/${document_id}`, cleanData);
      return response.data;
    }
    throw error;
  }
};

export const deleteDocument = async (document_id) => {
  const response = await api.delete(`documents/${document_id}`);
  return response.data;
};


// ################### Training API's ##################

export const createTraining = async ( TrainingData) => {
  const response = await api.post(`/training`, TrainingData);
  return response.data;
};

export const getTraining= async (identity_id , params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  const response = await api.get(`training?identity_id=${identity_id}`,  { params: query });
  return response.data.data.data || [];
};

export const updateTraining = async (training_id , TrainingData) => {
  const response = await api.put(`training/${training_id}`, TrainingData);
  return response.data;
};
export const deleteTraining = async (training_id ) => {
  const response = await api.delete(`training/${training_id}`);
  return response.data;
};

// ################### Badge Template API's ##################
export const createBadgeTemplate = async (templateData) => {
  const response = await api.post("badge-templates", templateData);
  return response.data;
};

export const getBadgeTemplates = async (params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  if (params.page) query.page = params.page;
  if (params.limit) query.limit = params.limit;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;
  const response = await api.get("badge-templates", { params: query });
  return response.data;
};

export const getBadgeTemplateById = async (templateId) => {
  const response = await api.get(`badge-templates/${templateId}`);
  return response.data;
};

export const updateBadgeTemplate = async (templateId, templateData) => {
  const response = await api.put(`badge-templates/${templateId}`, templateData);
  return response.data;
};

export const deleteBadgeTemplate = async (templateId) => {
  const response = await api.delete(`badge-templates/${templateId}`);
  return response.data;
};

// ################### Vehicle API's ##################
export const createVehicle = async ( VehicleData) => {
  const response = await api.post(`/vehicles`, VehicleData);
  return response.data;
};

export const getVehicle= async (identity_id , params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  const response = await api.get(`vehicles?identity_id=${identity_id}`,  { params: query });
  return response.data.data.data || [];
};

export const updateVehicle = async (vehicle_id , VehicleData) => {
  const response = await api.put(`vehicles/${vehicle_id}`, VehicleData);
  return response.data;
};
export const deleteVehicle = async (vehicle_id ) => {
  const response = await api.delete(`vehicles/${vehicle_id}`);
  return response.data;
};


