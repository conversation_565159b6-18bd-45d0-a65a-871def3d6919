import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress
} from '@mui/material';
import { toast } from 'react-toastify';
import { createBadgeTemplate } from '../../api/identity';

const CreateTemplateModal = ({ open, onClose, onSuccess }) => {
  const navigate = useNavigate();
  const [templateName, setTemplateName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleClose = () => {
    if (!loading) {
      setTemplateName('');
      setError('');
      onClose();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!templateName.trim()) {
      setError('Template name is required');
      return;
    }

    if (templateName.trim().length < 3) {
      setError('Template name must be at least 3 characters long');
      return;
    }

    if (templateName.trim().length > 100) {
      setError('Template name must be less than 100 characters');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const templateData = {
        name: templateName.trim()
      };

      const response = await createBadgeTemplate(templateData);

      toast.success('Template created successfully');

      // Navigate to the designer with the new template ID
      navigate(`/id-designer/${response.data.badge_template_id}`);
      
      // Close modal and refresh parent
      handleClose();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating template:', error);
      
      // Handle specific error messages
      if (error.response?.data?.message) {
        setError(error.response.data.message);
      } else if (error.response?.status === 409) {
        setError('A template with this name already exists');
      } else if (error.response?.status === 400) {
        setError('Invalid template data provided');
      } else {
        setError('Failed to create template. Please try again.');
      }
      
      toast.error('Failed to create template');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }
      }}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle sx={{ 
          pb: 1,
          fontSize: '20px',
          fontWeight: 600,
          color: '#1f2937'
        }}>
          Create New Badge Template
        </DialogTitle>
        
        <DialogContent sx={{ pt: 2 }}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 3 }}
          >
            Enter a name for your new badge template. The template will be created and you'll be redirected to the designer to customize it.
          </Typography>
          
          <TextField
            autoFocus
            label="Template Name"
            placeholder="e.g., Employee Badge, Visitor Pass, Contractor ID"
            value={templateName}
            onChange={(e) => {
              setTemplateName(e.target.value);
              if (error) setError('');
            }}
            error={!!error}
            helperText={error || 'Enter a descriptive name for your template'}
            fullWidth
            variant="outlined"
            disabled={loading}
            inputProps={{
              maxLength: 100
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: '#4F2683',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#4F2683',
                },
              },
              '& .MuiInputLabel-root.Mui-focused': {
                color: '#4F2683',
              }
            }}
          />
          
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              {templateName.length}/100 characters
            </Typography>
          </Box>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 3, pt: 1, gap: 1 }}>
          <Button
            onClick={handleClose}
            disabled={loading}
            sx={{
              textTransform: 'none',
              color: '#6b7280',
              '&:hover': {
                backgroundColor: 'rgba(107, 114, 128, 0.04)',
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading || !templateName.trim()}
            sx={{
              backgroundColor: '#4F2683',
              '&:hover': {
                backgroundColor: '#3d1f66',
              },
              '&:disabled': {
                backgroundColor: '#e5e7eb',
                color: '#9ca3af'
              },
              textTransform: 'none',
              fontWeight: 500,
              px: 3,
              minWidth: 120
            }}
          >
            {loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} color="inherit" />
                Creating...
              </Box>
            ) : (
              'Create & Edit'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateTemplateModal;
