import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  Card, 
  CardContent, 
  CardActions, 
  Grid, 
  Pagination, 
  IconButton,
  Chip,
  InputAdornment
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { getBadgeTemplates, deleteBadgeTemplate, updateBadgeTemplate } from '../../api/identity';
import CreateTemplateModal from '../../Components/TemplateManagement/CreateTemplateModal';

const TemplateManagement = () => {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTemplates, setTotalTemplates] = useState(0);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const templatesPerPage = 12;

  // Fetch templates
  const fetchTemplates = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: templatesPerPage,
        search: search.trim() || undefined,
        sortBy: 'created_at',
        sortOrder: 'desc'
      };
      
      const response = await getBadgeTemplates(params);
      setTemplates(response.data || []);
      setTotalPages(Math.ceil((response.total || 0) / templatesPerPage));
      setTotalTemplates(response.total || 0);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Failed to fetch templates');
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTemplates(currentPage, searchTerm);
  }, [currentPage]);

  // Search handler with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchTemplates(1, searchTerm);
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (event, page) => {
    setCurrentPage(page);
  };

  // Handle template deletion
  const handleDeleteTemplate = async (badgeTemplateId, templateName) => {
    if (window.confirm(`Are you sure you want to delete "${templateName}"? This action cannot be undone.`)) {
      try {
        await deleteBadgeTemplate(badgeTemplateId);
        toast.success('Template deleted successfully');
        fetchTemplates(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting template:', error);
        toast.error('Failed to delete template');
      }
    }
  };

  // Handle template edit/open
  const handleOpenTemplate = (badgeTemplateId) => {
    navigate(`/id-designer/${badgeTemplateId}`);
  };

  // Handle status toggle
  const handleToggleStatus = async (badgeTemplateId, currentStatus, templateName) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await updateBadgeTemplate(badgeTemplateId, { status: newStatus });
      toast.success(`Template "${templateName}" ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
      fetchTemplates(currentPage, searchTerm);
    } catch (error) {
      console.error('Error updating template status:', error);
      toast.error('Failed to update template status');
    }
  };

  // Handle create template success
  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    fetchTemplates(currentPage, searchTerm);
  };

  return (
    <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 'normal', 
            fontSize: '24px', 
            mb: 3, 
            color: '#4F2683' 
          }}
        >
          Badge Template Management
        </Typography>
        
        {/* Search and Create Section */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          mb: 3,
          gap: 2
        }}>
          <TextField
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ 
              maxWidth: 400,
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: '#4F2683',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#4F2683',
                },
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#9e9e9e' }} />
                </InputAdornment>
              ),
            }}
          />
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setIsCreateModalOpen(true)}
            sx={{
              backgroundColor: '#4F2683',
              '&:hover': {
                backgroundColor: '#3d1f66',
              },
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            }}
          >
            Create Template
          </Button>
        </Box>

        {/* Results Summary */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {loading ? 'Loading...' : `${totalTemplates} template${totalTemplates !== 1 ? 's' : ''} found`}
          </Typography>
          {searchTerm && (
            <Chip 
              label={`Search: "${searchTerm}"`} 
              size="small" 
              onDelete={() => setSearchTerm('')}
              sx={{ 
                backgroundColor: '#f3f4f6',
                '& .MuiChip-deleteIcon': {
                  color: '#6b7280'
                }
              }}
            />
          )}
        </Box>
      </Box>

      {/* Templates Grid */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography color="text.secondary">Loading templates...</Typography>
          </Box>
        ) : templates.length === 0 ? (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center', 
            height: 200,
            textAlign: 'center'
          }}>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
              {searchTerm ? 'No templates found' : 'No templates created yet'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm ? 'Try adjusting your search terms' : 'Create your first badge template to get started'}
            </Typography>
            {!searchTerm && (
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => setIsCreateModalOpen(true)}
                sx={{
                  borderColor: '#4F2683',
                  color: '#4F2683',
                  '&:hover': {
                    borderColor: '#3d1f66',
                    backgroundColor: 'rgba(79, 38, 131, 0.04)',
                  },
                  textTransform: 'none'
                }}
              >
                Create Template
              </Button>
            )}
          </Box>
        ) : (
          <Grid container spacing={3}>
            {templates.map((template) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={template.badge_template_id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                    },
                    border: '1px solid #e5e7eb'
                  }}
                  onClick={() => handleOpenTemplate(template.badge_template_id)}
                >
                  <CardContent sx={{ flexGrow: 1, pb: 1 }}>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontSize: '16px',
                        fontWeight: 600,
                        color: '#1f2937',
                        mb: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {template.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 1 }}
                    >
                      ID: {template.badge_template_id}
                    </Typography>
                    <Chip
                      label={template.status || 'active'}
                      size="small"
                      sx={{
                        backgroundColor: (template.status || 'active') === 'active' ? '#e8f5e8' : '#ffeaa7',
                        color: (template.status || 'active') === 'active' ? '#2d5016' : '#b7950b',
                        fontWeight: 500,
                        mb: 2
                      }}
                    />
                    <Typography 
                      variant="caption" 
                      color="text.secondary"
                      sx={{ display: 'block' }}
                    >
                      Created: {new Date(template.created_at).toLocaleDateString()}
                    </Typography>
                    {template.updated_at && template.updated_at !== template.created_at && (
                      <Typography 
                        variant="caption" 
                        color="text.secondary"
                        sx={{ display: 'block' }}
                      >
                        Modified: {new Date(template.updated_at).toLocaleDateString()}
                      </Typography>
                    )}
                  </CardContent>
                  <CardActions sx={{ pt: 0, justifyContent: 'space-between', flexWrap: 'wrap', gap: 1 }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenTemplate(template.badge_template_id);
                        }}
                        sx={{
                          color: '#4F2683',
                          textTransform: 'none',
                          '&:hover': {
                            backgroundColor: 'rgba(79, 38, 131, 0.04)',
                          }
                        }}
                      >
                        Edit
                      </Button>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(template.badge_template_id, template.status || 'active', template.name);
                        }}
                        sx={{
                          color: (template.status || 'active') === 'active' ? '#2d5016' : '#b7950b',
                          '&:hover': {
                            backgroundColor: (template.status || 'active') === 'active' ? 'rgba(45, 80, 22, 0.04)' : 'rgba(183, 149, 11, 0.04)',
                          }
                        }}
                        title={`${(template.status || 'active') === 'active' ? 'Deactivate' : 'Activate'} template`}
                      >
                        {(template.status || 'active') === 'active' ? <ToggleOnIcon /> : <ToggleOffIcon />}
                      </IconButton>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteTemplate(template.badge_template_id, template.name);
                      }}
                      sx={{
                        color: '#6b7280',
                        '&:hover': {
                          color: '#dc2626',
                          backgroundColor: 'rgba(220, 38, 38, 0.04)',
                        }
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          mt: 4, 
          pb: 2 
        }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            sx={{
              '& .MuiPaginationItem-root': {
                '&.Mui-selected': {
                  backgroundColor: '#4F2683',
                  '&:hover': {
                    backgroundColor: '#3d1f66',
                  }
                }
              }
            }}
          />
        </Box>
      )}

      {/* Create Template Modal */}
      <CreateTemplateModal
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};

export default TemplateManagement;
